import { SidebarAdZone } from '@/components/ads';
import { CollapsibleNavGroup } from '@/components/collapsible-nav-group';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import {
    Activity,
    BarChart3,
    Bell,
    Building2,
    Cog,
    CreditCard,
    Database,
    DollarSign,
    FileText,
    FolderOpen,
    Grid3X3,
    Heart,
    History,
    Image as ImageIcon,
    Key,
    LayoutGrid,
    Lock,
    Mail,
    Menu,
    MessageSquare,
    Package,
    CreditCard as PaymentIcon,
    Search,
    Settings,
    Shield,
    ShieldCheck,
    Smartphone,
    Tags,
    Target,
    Upload,
    User,
    Users,
    Wallet
} from 'lucide-react';
import { useEffect, useState } from 'react';
import AppLogo from './app-logo';

// Development environment debugging
const isDevelopment = import.meta.env.DEV;

// Enhanced logging utility for development
const debugLog = (message: string, data?: any) => {
    if (isDevelopment) {
        console.log(`[AppSidebar Debug] ${message}`, data || '');
    }
};

// Safe route helper with error handling
const safeRoute = (routeName: string, fallbackUrl: string = '#'): string => {
    try {
        if (typeof route === 'function') {
            return route(routeName);
        } else {
            debugLog(`Route helper not available, using fallback for: ${routeName}`);
            return fallbackUrl;
        }
    } catch (error) {
        debugLog(`Route error for ${routeName}:`, error);
        return fallbackUrl;
    }
};

// User Dashboard Navigation
const dashboardNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
];

// Search & Browse Navigation
const searchNavItems: NavItem[] = [
    {
        title: 'Search Parts',
        href: '/search',
        icon: Search,
    },
    {
        title: 'Search Model',
        href: '/search/model',
        icon: Building2,
    },
    {
        title: 'Search in Category',
        href: '/search/categories',
        icon: Grid3X3,
    },
    {
        title: 'Search in Brand',
        href: '/search/brands',
        icon: Smartphone,
    },
];

// User Activity Navigation - Function to handle locked states
const getActivityNavItems = (isPremium: boolean): NavItem[] => [
    {
        title: 'Notifications',
        href: '/notifications',
        icon: Bell,
    },
    {
        title: 'Activity Log',
        href: '/activity',
        icon: Activity,
    },
    {
        title: 'Favorites',
        href: '/dashboard/favorites',
        icon: Heart,
        isLocked: !isPremium,
        lockReason: 'Upgrade to Premium to access your favorites'
    },
    {
        title: 'Search History',
        href: '/dashboard/history',
        icon: History,
    },
    {
        title: 'Usage Stats',
        href: '/subscription/search-stats',
        icon: BarChart3,
    },
];

// Support & Contact Navigation
const supportNavItems: NavItem[] = [
    {
        title: 'Contact Support',
        href: '/contact',
        icon: MessageSquare,
    },
    {
        title: 'Contact History',
        href: '/contact-history',
        icon: History,
    },
    {
        title: 'Check Status',
        href: '/contact/status',
        icon: Search,
    },
];

// Subscription Navigation
const subscriptionNavItems: NavItem[] = [
    {
        title: 'Subscription Plans',
        href: '/subscription/plans',
        icon: CreditCard,
    },
    {
        title: 'Billing Dashboard',
        href: '/subscription/dashboard',
        icon: BarChart3,
    },
    {
        title: 'Payment Requests',
        href: '/payment-requests',
        icon: FileText,
    },
];

// Admin Navigation - Core Administration
const adminCoreNavItems: NavItem[] = [
    {
        title: 'Admin Dashboard',
        href: '/admin/dashboard',
        icon: Shield,
    },
    {
        title: 'User Management',
        href: '/admin/users',
        icon: Users,
    },
    {
        title: 'Contact Submissions',
        href: '/admin/contact-submissions',
        icon: MessageSquare,
    },
    {
        title: 'Analytics',
        href: safeRoute('admin.analytics.index', '/admin/analytics'),
        icon: BarChart3,
    },
    {
        title: 'Popular Parts',
        href: safeRoute('admin.popular-parts.index', '/admin/popular-parts'),
        icon: Heart,
    },
];

// Admin Navigation - Financial Management
const adminFinancialNavItems: NavItem[] = [
    {
        title: 'Payment Requests',
        href: '/admin/payment-requests',
        icon: CreditCard,
    },
    {
        title: 'Subscription Manager',
        href: '/admin/subscriptions',
        icon: CreditCard,
    },
    {
        title: 'Pricing Plans',
        href: '/admin/pricing-plans',
        icon: DollarSign,
    },
];

// Admin Navigation - Security & Access
const adminSecurityNavItems: NavItem[] = [
    {
        title: 'Impersonation Logs',
        href: '/admin/impersonation/logs',
        icon: Activity,
    },
    {
        title: 'User Activities',
        href: '/admin/activities',
        icon: FileText,
    },
    {
        title: 'Two-Factor Auth',
        href: '/admin/two-factor',
        icon: Key,
    },
    {
        title: 'Rate Limiting',
        href: '/admin/rate-limit',
        icon: Shield,
    },
];

// Admin Navigation - System Configuration
const adminSystemNavItems: NavItem[] = [
    {
        title: 'Site Settings',
        href: '/admin/site-settings',
        icon: Settings,
    },
    {
        title: 'Search Configuration',
        href: '/admin/search-config',
        icon: Search,
    },
    {
        title: 'Email Config',
        href: '/admin/email-config',
        icon: Mail,
    },
    {
        title: 'Google Analytics',
        href: '/admin/google-analytics',
        icon: BarChart3,
    },
    {
        title: 'Meta Pixel',
        href: '/admin/meta-pixel',
        icon: BarChart3,
    },
    {
        title: 'Ad Management',
        href: '/admin/ads',
        icon: Target,
    },
    {
        title: 'Notifications',
        href: '/admin/notifications',
        icon: Bell,
    },
    {
        title: 'Payment Gateways',
        href: '/admin/payment-gateways',
        icon: PaymentIcon,
    },
];

// Admin Navigation - Pages & Menus
const adminPagesMenusNavItems: NavItem[] = [
    {
        title: 'Pages',
        href: '/admin/pages',
        icon: FileText,
    },
    {
        title: 'Menus',
        href: '/admin/menus',
        icon: Menu,
    },
];

// Admin Navigation - Content Management
const adminContentNavItems: NavItem[] = [
    {
        title: 'Categories',
        href: '/admin/categories',
        icon: Tags,
    },
    {
        title: 'Parts',
        href: '/admin/parts',
        icon: Package,
    },
    {
        title: 'Brands',
        href: '/admin/brands',
        icon: Smartphone,
    },
    {
        title: 'Models',
        href: '/admin/models',
        icon: Database,
    },
    {
        title: 'Bulk Import',
        href: '/admin/bulk-import',
        icon: Upload,
    },
    {
        title: 'Media Library',
        href: '/admin/media',
        icon: ImageIcon,
    },
];

// Settings Navigation
const settingsNavItems: NavItem[] = [
    {
        title: 'Settings',
        href: '/settings/profile',
        icon: Settings,
    },
];

// Key for storing view preference in localStorage
const VIEW_MODE_STORAGE_KEY = 'admin_view_mode';

export function AppSidebar() {
    const { auth } = usePage<SharedData>().props;

    // Enhanced admin detection with proper backend integration and debugging
    const isAdmin: boolean = (() => {
        try {
            // Check if user exists
            if (!auth?.user) {
                debugLog('No authenticated user found');
                return false;
            }

            // Use backend-provided isAdmin value only - no email fallback
            const adminStatus = Boolean(auth.user.isAdmin);

            debugLog('Admin detection:', {
                userEmail: auth.user.email,
                adminStatus,
                userObject: auth.user
            });

            return adminStatus;
        } catch (error) {
            debugLog('Error in admin detection:', error);
            return false;
        }
    })();

    // Content Manager detection
    const isContentManager: boolean = (() => {
        try {
            if (!auth?.user) {
                return false;
            }

            // Check if user has content_manager role
            const hasRole = 'role' in auth.user && auth.user.role === 'content_manager';

            debugLog('Content Manager detection:', {
                userEmail: auth.user.email,
                userRole: auth.user.role,
                isContentManager: hasRole,
                userObject: auth.user
            });

            return hasRole;
        } catch (error) {
            debugLog('Error in content manager detection:', error);
            return false;
        }
    })();

    // Check if user can manage content (admin or content manager)
    const canManageContent = isAdmin || isContentManager;

    const isPremium = auth.user?.subscription_plan === 'premium';

    // State to track if user is viewing admin menu (default to admin view for admins/content managers, user view for regular users)
    const [isAdminView, setIsAdminView] = useState<boolean>(canManageContent);

    // Load saved preference from localStorage on component mount
    useEffect(() => {
        try {
            if (canManageContent) {
                const savedViewMode = localStorage.getItem(VIEW_MODE_STORAGE_KEY);
                debugLog('Loading saved view mode:', savedViewMode);

                if (savedViewMode !== null) {
                    const newAdminView = savedViewMode === 'admin';
                    setIsAdminView(newAdminView);
                    debugLog('Set admin view from localStorage:', newAdminView);
                }
            } else {
                // Ensure regular users are always in user view
                setIsAdminView(false);
                debugLog('Regular user, forcing user view');
            }
        } catch (error) {
            debugLog('Error loading view preference:', error);
            setIsAdminView(canManageContent); // Fallback to default
        }
    }, [canManageContent]);

    // Save preference to localStorage when it changes
    useEffect(() => {
        try {
            if (canManageContent) {
                const viewMode = isAdminView ? 'admin' : 'user';
                localStorage.setItem(VIEW_MODE_STORAGE_KEY, viewMode);
                debugLog('Saved view mode to localStorage:', viewMode);
            }
        } catch (error) {
            debugLog('Error saving view preference:', error);
        }
    }, [isAdminView, canManageContent]);

    // Debug current state
    useEffect(() => {
        debugLog('AppSidebar state update:', {
            isAdmin,
            isAdminView,
            isPremium,
            userEmail: auth.user?.email
        });
    }, [isAdmin, isAdminView, isPremium, auth.user?.email]);

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton
                            size="lg"
                            asChild
                            tooltip={{ children: "FixHaat - Mobile Parts Database" }}
                        >
                            <Link href={canManageContent && isAdminView ? "/admin/dashboard" : "/dashboard"} prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                {/* Show Admin/Content Management Navigation when user can manage content and is in admin view */}
                {canManageContent && isAdminView ? (
                    <>
                        {/* Admin Core Section - Only for full admins */}
                        {isAdmin && (
                            <CollapsibleNavGroup
                                title="Core Administration"
                                items={adminCoreNavItems}
                                groupId="admin-core"
                                icon={ShieldCheck}
                            />
                        )}

                        {/* Admin Financial Section - Only for full admins */}
                        {isAdmin && (
                            <CollapsibleNavGroup
                                title="Financial Management"
                                items={adminFinancialNavItems}
                                groupId="admin-financial"
                                icon={Wallet}
                            />
                        )}

                        {/* Admin Security Section - Only for full admins */}
                        {isAdmin && (
                            <CollapsibleNavGroup
                                title="Security & Access"
                                items={adminSecurityNavItems}
                                groupId="admin-security"
                                icon={Lock}
                            />
                        )}

                        {/* Admin System Section - Only for full admins */}
                        {isAdmin && (
                            <CollapsibleNavGroup
                                title="System Configuration"
                                items={adminSystemNavItems}
                                groupId="admin-system"
                                icon={Cog}
                            />
                        )}

                        {/* Pages & Menus Section - Available for both admins and content managers */}
                        <CollapsibleNavGroup
                            title="Pages & Menus"
                            items={adminPagesMenusNavItems}
                            groupId="admin-pages-menus"
                            icon={FileText}
                        />

                        {/* Content Management Section - Available for both admins and content managers */}
                        <CollapsibleNavGroup
                            title="Content Management"
                            items={adminContentNavItems}
                            groupId="admin-content"
                            icon={FolderOpen}
                        />
                    </>
                ) : (
                    <>
                        {/* Dashboard Section */}
                        <CollapsibleNavGroup
                            title="Platform"
                            items={dashboardNavItems}
                            groupId="user-platform"
                            icon={LayoutGrid}
                        />

                        {/* Search & Browse Section */}
                        <CollapsibleNavGroup
                            title="Search & Browse"
                            items={searchNavItems}
                            groupId="user-search"
                            icon={Search}
                        />

                        {/* User Activity Section - Show all items for premium users, limited for free users */}
                        <CollapsibleNavGroup
                            title="Activity"
                            items={getActivityNavItems(isPremium)}
                            groupId="user-activity"
                            icon={Activity}
                        />

                        {/* Subscription Section - Show different items based on subscription status */}
                        <CollapsibleNavGroup
                            title={isPremium ? "Subscription" : "Upgrade"}
                            items={subscriptionNavItems}
                            groupId="user-subscription"
                            icon={CreditCard}
                        />

                        {/* Support Section */}
                        <CollapsibleNavGroup
                            title="Support"
                            items={supportNavItems}
                            groupId="user-support"
                            icon={MessageSquare}
                        />
                    </>
                )}

                {/* Settings Section */}
                <CollapsibleNavGroup
                    title="Account"
                    items={settingsNavItems}
                    groupId="account"
                    icon={User}
                />

                {/* Sidebar Ad Zone - Only for free users */}
                <div className="mt-4 px-2">
                    <SidebarAdZone
                        page={window.location.pathname}
                        className="sticky top-4"
                    />
                </div>
            </SidebarContent>

            <SidebarFooter>
                {auth.user ? (
                    <NavUser isAdmin={canManageContent} isAdminView={isAdminView} setIsAdminView={setIsAdminView} />
                ) : (
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <div className="flex flex-col gap-2 p-2">
                                <Link href={route('login')}>
                                    <SidebarMenuButton size="sm" className="w-full justify-center">
                                        <User className="h-4 w-4" />
                                        Log in
                                    </SidebarMenuButton>
                                </Link>
                                <Link href={route('register')}>
                                    <SidebarMenuButton size="sm" className="w-full justify-center bg-blue-600 text-white hover:bg-blue-700">
                                        Sign up
                                    </SidebarMenuButton>
                                </Link>
                            </div>
                        </SidebarMenuItem>
                    </SidebarMenu>
                )}
            </SidebarFooter>
        </Sidebar>
    );
}
