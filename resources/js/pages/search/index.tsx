import { ContentAdSeparator, FooterAdZone, HeaderAdZone } from '@/components/ads';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { UnifiedSearchInterface } from '@/components/unified-search-interface';
import { useAds } from '@/hooks/use-ads';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import {
    Package,
    Smartphone,
    Tag,
    TrendingUp
} from 'lucide-react';
import { useState } from 'react';

interface Category {
    id: number;
    name: string;
}

interface Brand {
    id: number;
    name: string;
}

interface Filters {
    categories: Category[];
    brands: Brand[];
    manufacturers: string[];
    release_years: number[];
}

interface StatisticItem {
    count: number;
    formatted: string;
    label: string;
}

interface Statistics {
    parts: StatisticItem;
    models: StatisticItem;
    categories: StatisticItem;
    brands?: StatisticItem;
}

interface Props {
    filters: Filters;
    statistics: Statistics;
}

export default function SearchIndex({ filters, statistics }: Props) {
    const { shouldShowAds, canShowZone } = useAds();
    const [searchQuery, setSearchQuery] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const popularSearches = [
        { label: 'iPhone Display', type: 'part' },
        { label: 'Samsung Battery', type: 'part' },
        { label: 'Camera Module', type: 'category' },
        { label: 'Charging IC', type: 'category' },
        { label: 'OnePlus', type: 'brand' },
        { label: 'Xiaomi', type: 'brand' },
    ];

    return (
        <AppLayout>
            <Head title="Search Parts" />

            {/* Header Ad Zone */}
            {shouldShowAds && canShowZone('header') && (
                <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                    <div className="max-w-7xl mx-auto px-4 py-2">
                        <HeaderAdZone page="/search" className="w-full flex justify-center" />
                    </div>
                </div>
            )}

            <div className="py-12">
                <div className="max-w-6xl mx-auto sm:px-6 lg:px-8">
                    {/* Hero Section */}
                    <div className="text-center mb-12">
                        <h1 className="text-4xl font-bold text-gray-900 mb-4">
                            Find Mobile Parts
                        </h1>
                        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                            Search through our comprehensive database of mobile device parts and components
                        </p>
                    </div>

                    {/* Search Form */}
                    <Card className="mb-8">
                        <CardContent className="p-6">
                            <UnifiedSearchInterface
                                searchQuery={searchQuery}
                                setSearchQuery={setSearchQuery}
                                isAuthenticated={true}
                                isLoading={isLoading}
                                setIsLoading={setIsLoading}
                                showFilters={true}
                                showSuggestions={true}
                                size="lg"
                                filters={filters}
                            />
                        </CardContent>
                    </Card>

                    {/* Content Ad Separator */}
                    {shouldShowAds && canShowZone('content') && (
                        <ContentAdSeparator page="/search" spacing="my-8" />
                    )}

                    {/* Popular Searches */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="w-5 h-5" />
                                Popular Searches
                            </CardTitle>
                            <CardDescription>
                                Quick access to commonly searched items
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-2">
                                {popularSearches.map((search, index) => (
                                    <Button
                                        key={index}
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                            setSearchQuery(search.label);
                                            setSearchType(search.type);
                                        }}
                                        className="text-sm"
                                    >
                                        {search.label}
                                    </Button>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Quick Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <Card>
                            <CardContent className="p-6 text-center">
                                <Package className="w-12 h-12 mx-auto mb-4 text-green-600" />
                                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                                    {statistics?.parts?.formatted || '0+'}
                                </h3>
                                <p className="text-gray-600">
                                    {statistics?.parts?.label || 'Parts Available'}
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6 text-center">
                                <Smartphone className="w-12 h-12 mx-auto mb-4 text-blue-600" />
                                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                                    {statistics?.models?.formatted || '0+'}
                                </h3>
                                <p className="text-gray-600">
                                    {statistics?.models?.label || 'Mobile Models'}
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6 text-center">
                                <Tag className="w-12 h-12 mx-auto mb-4 text-purple-600" />
                                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                                    {statistics?.categories?.formatted || '0+'}
                                </h3>
                                <p className="text-gray-600">
                                    {statistics?.categories?.label || 'Categories'}
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Footer Ad Zone */}
                {shouldShowAds && canShowZone('footer') && (
                    <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 mt-8">
                        <div className="max-w-7xl mx-auto px-4 py-4">
                            <FooterAdZone page="/search" className="w-full flex justify-center" />
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
