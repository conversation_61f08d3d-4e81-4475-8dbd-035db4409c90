<?php

namespace App\Providers;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Observers\SearchStatisticsObserver;
use App\Services\OtpService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register OTP service as singleton
        $this->app->singleton(OtpService::class, function ($app) {
            return new OtpService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register observers for search statistics cache invalidation
        Part::observe(SearchStatisticsObserver::class);
        MobileModel::observe(SearchStatisticsObserver::class);
        Category::observe(SearchStatisticsObserver::class);
        Brand::observe(SearchStatisticsObserver::class);
    }
}
