<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\Subscription;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateTestUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:create-test
                            {type=free : The type of user to create (free, premium, admin)}
                            {--email= : Custom email address}
                            {--name= : Custom name}
                            {--password=password : Password for the user}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test user for development and testing purposes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');
        $email = $this->option('email') ?: "{$type}@test.com";
        $name = $this->option('name') ?: ucfirst($type) . ' Test User';
        $password = $this->option('password');

        // Validate type
        if (!in_array($type, ['free', 'premium', 'admin'])) {
            $this->error('Invalid user type. Must be: free, premium, or admin');
            return 1;
        }

        // Check if user already exists
        if (User::where('email', $email)->exists()) {
            $this->error("User with email {$email} already exists!");
            return 1;
        }

        // Create user based on type
        $userData = [
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved',
        ];

        switch ($type) {
            case 'free':
                $userData['subscription_plan'] = 'free';
                break;

            case 'premium':
                $userData['subscription_plan'] = 'premium';
                break;

            case 'admin':
                $userData['subscription_plan'] = 'premium'; // Admins get premium features
                $userData['is_admin'] = true;
                $userData['role'] = 'admin';
                break;
        }

        $user = User::create($userData);

        // Create subscription for premium users
        if ($type === 'premium' || $type === 'admin') {
            $this->createPremiumSubscription($user);
        }

        $this->info("✅ {$type} user created successfully!");
        $this->info("📧 Email: {$email}");
        $this->info("🔑 Password: {$password}");

        if ($type === 'free') {
            $this->info("🔒 This user will see locked favorites with upgrade prompts and ads");
        } else {
            $this->info("⭐ This user has full access to favorites functionality");
        }

        return 0;
    }

    /**
     * Create premium subscription for user
     */
    private function createPremiumSubscription(User $user): void
    {
        // Find or create premium pricing plan
        $premiumPlan = PricingPlan::firstOrCreate(
            ['name' => 'premium'],
            [
                'display_name' => 'Premium',
                'description' => 'Premium subscription plan',
                'price' => 19.00,
                'currency' => 'USD',
                'interval' => 'month',
                'features' => ['Unlimited searches', 'Access to favorites', 'Ad-free experience'],
                'is_active' => true,
                'is_public' => true,
            ]
        );

        // Create active subscription
        Subscription::create([
            'user_id' => $user->id,
            'pricing_plan_id' => $premiumPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'current_period_start' => now()->subDays(5),
            'current_period_end' => now()->addDays(25),
            'payment_gateway' => 'test',
        ]);
    }
}
